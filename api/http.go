package api

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"time"

	fiber "github.com/gofiber/fiber/v3"
)

type errorResponse struct {
	Body errorBody `json:"error"`
}

type errorBody struct {
	Type    string `json:"type"`
	Message string `json:"message"`
}

func (h *handler) new() *fiber.App {
	api := fiber.New(fiber.Config{
		ErrorHandler: func(c fiber.Ctx, err error) error {
			return c.Status(fiber.StatusInternalServerError).JSON(errorResponse{Body: errorBody{Type: "service_error", Message: err.Error()}})
		},
		UnescapePath: true,
		// DisableHeaderNormalizing: true,
		JSONEncoder: json.Marshal,
		JSONDecoder: json.Unmarshal,
	})

	api.Use(h.fiberLogger)

	api.Get("/health", func(c fiber.Ctx) error { return c.SendStatus(http.StatusOK) })

	// ------------------------------------------------------------------------------
	// companies

	api.Get("/company/:corp_id", h.GetCorpBalance)
	api.Put("/corppay/:corp_id/:amount/:amount_type/:description?", h.CorpPay)
	api.Post("/company/:corp_id/:name/:reg_date/:phone/:account/:inn", h.CreateCorp)
	api.Put("/company/:corp_id/:name/:reg_date/:phone/:account/:inn", h.UpdateCorp)
	api.Get("/accountForOperations/:operation_name", h.GetOperations)

	// ------------------------------------------------------------------------------
	// corp clients

	api.Post("/corpclient/:client_id/:phone/:first_name/:last_name/:corp_id", h.CreateCorpClient)
	api.Put("/corpclient/:client_id/:phone/:first_name/:last_name/:corp_id", h.UpdateCorpClient)
	api.Get("/availablecorpclient/:client_id/:corp_id", h.GetCorpClientBalance)
	api.Get("/corpclient-limit/:client_id/:corp_id", h.GetCorpClientLimit)
	api.Post("/corpclient-limit/:client_id/:corp_id/:amount/:repeat", h.CreateCorpClientLimit)
	api.Put("/corpclient-limit/:client_id/:corp_id/:amount/:repeat", h.UpdateCorpClientLimit)
	api.Delete("/corpclient-limit/:client_id/:corp_id", h.DeleteCorpClientLimit)

	// ------------------------------------------------------------------------------
	// clients

	// profile
	api.Post("/client/:client_id/:phone/:first_name/:last_name", h.CreateClient)
	api.Put("/client/:client_id/:phone/:first_name/:last_name", h.UpdateClient)
	api.Delete("/client/:client_id", h.DeleteClient)
	api.Put("/client-bonus-mode/:client_id/:mode", h.UpdateClientCashbackMode)

	// payment types
	api.Get("/v1/clients/:client_id/payment-types", h.GetClientPaymentTypes)
	api.Get("/v1/paycom/clients/:client_id/debts", h.GetClientDebts)
	api.Get("/v1/clients/:client_id/bonus-balance", h.GetClientCashbackOld)
	api.Get("/v2/clients/:client_id/payment-type", h.GetClientPaymentTypesOld) // TODO: deprecated
	api.Get("/v1/clients/:client_id/promocode-info", h.GetClientPromocodeInfo) // TODO: deprecated

	// card
	api.Get("/v2/paycom/clients/:client_id/cards", h.GetClientPaymeCards)
	api.Post("/v1/paycom/clients/:client_id/cards", h.AddClientPaymeCard)
	api.Delete("/v2/paycom/clients/:client_id/cards", h.DeleteClientPaymeCard)
	api.Get("/v1/cards/token", h.AddAtmosCard)
	api.Get("/v1/users/:user_id/cards", h.GetAtmosCards)
	api.Delete("/v1/users/:user_id/cards", h.DeleteCard)

	// payment
	api.Post("/v1/paycom/orders/:order_id/tips", h.PayTips)
	api.Post("/v1/paycom/clients/:client_id/paydebt", h.PayDebt)

	// ------------------------------------------------------------------------------
	// drivers

	// profile
	api.Post("/driver/:driver_id/:phone/:first_name/:last_name/:start_date/:park_id/:rate", h.CreateDriver)
	api.Put("/driver/:driver_id/:phone/:first_name/:last_name/:start_date/:park_id/:rate", h.UpdateDriver)
	api.Put("/driver-active/:driver_id", h.ActivateDriver)
	api.Delete("/driver-fire/:driver_id", h.DeactivateDriver)

	// balance
	api.Get("/v1/drivers/:driver_id/balance", h.GetDriverBalance)
	api.Get("/v1/drivers/:driver_id/debit-credit", h.GetDriverBalanceHistory)
	api.Post("/v1/drivers/:driver_id/money-transfer", h.DriverMoneyTransfer)
	api.Post("/v1/drivers/:driver_id/money-transfer-confirm", h.DriverMoneyTransferConfirm)
	api.Get("/v1/paycom/orders/:order_id/card-balance", h.GetOrderCardBalance)
	api.Put("/driver-transfer-money/:driver_id/:amount/:account?", h.DriverRefineBalance)
	api.Post("/v1/drivers/:driver_id/tax", h.DriverTax)

	// card
	api.Get("/v2/paycom/drivers/:driver_id/cards", h.GetDriverCard)
	api.Post("/v2/paycom/drivers/:driver_id/cards", h.AddDriverCard)
	api.Delete("/v1/paycom/drivers/:driver_id/cards", h.DeleteDriverCard)

	api.Get("/v1/paycom/tips", h.GetOrderTips)
	api.Get("/v1/drivers/:driver_id/fallbacks", h.GetDriverFallbackLinks)

	// ------------------------------------------------------------------------------
	// xpanel
	api.Get("/v1/orders/:order_id/payments", h.XpanelGetClientVisaPayments)
	api.Get("/v1/xpanel/a2c", h.XpanelGetDriverA2CPayments)
	api.Get("/v1/paycom/orders/:order_id/payments", h.XpanelGetClientPaymePayments)
	api.Post("/v1/paycom/cancel/orders", h.XpanelCancelPayment)
	api.Post("/v1/transactions/:transaction_id/cancel", h.XpanelCancelPayment)
	api.Get("/v1/paycom/orders/queue", h.GetOrdersPaymentQueue)
	api.Get("/v1/paycom/client-debts", h.XpanelGetClientDebts)
	api.Post("/v1/paycom/client-debts", h.XpanelCreateClientDebt)
	api.Put("/v1/paycom/client-debts/:debt_id/update-debt", h.XpanelUpdateClientDebtStatus)
	api.Get("/v1/xpanel/all-balance", h.XpanelGetAllBalance)
	api.Get("/v1/xpanel/corporate-balance", h.XpanelGetProviderAccountBalance)
	api.Get("/v1/xpanel/order-pay", h.XpanelGetOrderIsSuspicious)
	api.Post("/v1/xpanel/order-pay", h.XpanelPaySuspiciousOrder)
	api.Get("/v1/promocodes", h.XpanelGetPromocodes)
	api.Post("/v1/promocodes", h.XpanelAddPromocode)
	api.Put("/v1/promocodes", h.XpanelUpdatePromocode)
	api.Get("/v1/paycom/client-cards", h.XpanelGetClientCards)
	api.Get("/v1/paycom/driver-cards", h.XpanelGetDriverCards)
	api.Get("/taxipark/:park_id", h.GetParkBalance)
	api.Post("/taxipark/:park_id", h.CreatePark)
	api.Put("/taxipark/:park_id", h.UpdatePark)
	api.Put("/partner-to-taxipark", h.PartnerToPark)
	api.Get("/partner/:partner_id", h.GetPartner)
	api.Put("/partner/:partner_id", h.UpdatePartner)
	api.Post("/partner/:partner_id", h.CreatePartner)
	api.Put("/partner/:partner_id/deposit", h.DepositPartner)
	api.Get("/partner/:partner_id/balance", h.GetPartnerBalance)
	api.Put("/driverpay/:driver_id/:amount/:amount_type/:description?/:account_number?", h.RefillDriverBalance)
	api.Put("/driver-bonus/:driver_id/:amount/:amount_type/:description/:account_number", h.RefillDriverBalance)
	api.Get("/driver/balance-history/:driver_id/:limit/:skip", h.GetDriverBalanceHistoryV2)

	api.Get("/api/v1/transactions", h.XpanelGetBillingPayments)
	api.Get("/api/v1/account", h.XpanelGetBillingAccounts)
	api.Post("/api/v1/refill/payment-gateway", h.XpanelRefillBillingPaymentGatewayBalance)
	// api.Get("/api/v1/account/:acc_number", h.XpanelGetBillingPayments)
	// api.Get("/api/v1/balance", h.XpanelGetBillingBalance)

	api.Get("/v1/a2a/balance", h.GetA2ABalance)
	api.Get("/v1/a2a/:order_id", h.GetA2AForOrder)
	api.Post("/v1/a2a/:transaction_id/cancel", h.CancelA2ATransaction)
	api.Post("/v1/a2a/:transaction_id/update", h.UpdateA2ATransaction)
	api.Get("/v1/a2a/transfers/all", h.GetA2aTransactions)

	// ------------------------------------------------------------------------------
	// internal

	api.Get("/v1/clients/:client_id/cards/:card_id/solvency", h.GetClientCardSolvency)
	api.Get("/v1/clients/:client_id/cashback", h.GetClientCashback)
	api.Get("/v1/clients/:client_id/cards/:card_id", h.GetCardInfo)
	api.Get("/infopromocode/:client_id/:promocode", h.GetPromocodeInfo)
	api.Get("/validatepromocode/:client_id/:promocode", h.GetPromocodeValidation)
	api.Get("/v1/users/:user_id/debts", h.GetCorpDebt)

	// ------------------------------------------------------------------------------
	// webhooks
	api.Post("/v1/webhook/paynet", h.PaynetWebhook)
	api.Post("/v1/webhook/click", h.ClickWebhook)
	api.Post("/v1/webhook/atmos", h.AtmosWebhook)

	api.Post("/v1/paycom/merchant/driver-refill", h.PaymeDriverWebhook)
	api.Post("/v1/paycom/merchant/order-debts", h.PaymeClientWebhook)

	// ------------------------------------------------------------------------------

	api.Get("/v1/click/link", h.GetClickSuperAppPaymentLink)
	api.Post("/v1/click/:order_id/create-hold", h.CreateClickHold)
	api.Post("/ubk-ping", h.UbkPing)

	// ------------------------------------------------------------------------------

	api.Use(func(c fiber.Ctx) error {
		return c.Status(fiber.StatusNotFound).SendString("API not found!")
	})

	return api
}

func (h *handler) errorResponse(c fiber.Ctx, errType, msg string) error {
	return c.Status(http.StatusBadRequest).JSON(errorResponse{Body: errorBody{Type: errType, Message: msg}})
}

func (h *handler) badRequestResponse(c fiber.Ctx, msg string) error {
	return c.Status(http.StatusBadRequest).JSON(errorResponse{Body: errorBody{Type: "bad_request", Message: msg}})
}

func (h *handler) serviceErrorResponse(c fiber.Ctx, msg string) error {
	return c.Status(http.StatusInternalServerError).JSON(errorResponse{Body: errorBody{Type: "service_error", Message: msg}})
}

const requestTimeout = 10 * time.Second

func (h *handler) fiberLogger(c fiber.Ctx) (err error) {
	defer func() {
		if r := recover(); r != nil {
			var ok bool
			if err, ok = r.(error); !ok {
				err = fmt.Errorf("%v", r)
			}
		}
	}()

	start := time.Now()

	ctx, cancel := context.WithDeadline(h.ctx, start.Add(requestTimeout))
	defer cancel()

	c.SetContext(ctx)

	err = c.Next()

	latency := time.Since(start)

	req := c.Request()
	path := string(req.URI().PathOriginal())
	query := string(req.URI().QueryString())

	if query != "" {
		path += "?" + query
	}

	resp := c.Response()
	code := resp.StatusCode()

	if code != fiber.StatusOK {
		path += "    " + string(resp.Body())
	}

	msg := fmt.Sprintf("[FIBER]  | %3d | %13v | %-7s %s\n",
		code,
		latency,
		c.Method(),
		path,
	)

	fmt.Fprint(os.Stdout, msg)

	return
}
